using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using Xunit;
using Xunit.Abstractions;
using Xunit.Extensions.Logging;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Integration;

/// <summary>
/// Integration tests for the complete Polygon-based symbol universe workflow
/// These tests require actual Polygon API access and should be run with valid credentials
/// </summary>
[Collection("Integration")]
public class PolygonUniverseWorkflowIntegrationTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly IHost _host;
    private readonly IServiceProvider _serviceProvider;

    public PolygonUniverseWorkflowIntegrationTests(ITestOutputHelper output)
    {
        _output = output;

        // Build test host with all required services
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices(services =>
            {
                // Configuration
                var configuration = new ConfigurationBuilder()
                    .AddInMemoryCollection(new Dictionary<string, string?>
                    {
                        ["POLY_API_KEY"] = Environment.GetEnvironmentVariable("POLY_API_KEY") ?? "test_key",
                        ["REDIS_URL"] = "localhost:6379",
                        ["REDIS_DATABASE"] = "1", // Use test database
                        ["UsePolygonUniverse"] = "true"
                    })
                    .Build();

                services.AddSingleton<IConfiguration>(configuration);

                // Logging
                services.AddLogging(builder => builder.AddXUnit(output));

                // HTTP client factory
                services.AddHttpClient();

                // Rate limiting
                services.AddSingleton<IRateLimitPolicyFactory, RateLimitPolicyFactory>();

                // Polygon services
                services.AddSingleton<IPolygonClientFactory, PolygonClientFactory>();
                services.AddSingleton<IPolygonSymbolUniverseService, PolygonSymbolUniverseService>();
                services.AddSingleton<IPolygonSymbolSnapshotService, PolygonSymbolSnapshotService>();

                // Universe services
                services.AddSingleton<IDailyUniverseRefreshService, DailyUniverseRefreshService>();
                services.AddSingleton<IWebSocketSymbolSubscriptionManager, WebSocketSymbolSubscriptionManager>();
                services.AddSingleton<IMarketScheduleCoordinatorService, MarketScheduleCoordinatorService>();

                // WebSocket client (mock for testing)
                services.AddSingleton<IPolygonWebSocketClient, MockPolygonWebSocketClient>();

                // Market data service
                services.AddSingleton<IMarketDataService, MarketDataService>();

                // Updated DynamicUniverseProvider with Polygon integration
                services.AddSingleton<IDynamicUniverseProvider, DynamicUniverseProvider>();
            })
            .Build();

        _serviceProvider = _host.Services;
    }

    [Fact]
    [Trait("Category", "Integration")]
    public async Task PolygonSymbolUniverseService_ShouldFetchSymbols_FromRealApi()
    {
        // Skip if no API key provided
        var apiKey = Environment.GetEnvironmentVariable("POLY_API_KEY");
        if (string.IsNullOrEmpty(apiKey))
        {
            _output.WriteLine("Skipping integration test - POLY_API_KEY not provided");
            return;
        }

        // Arrange
        var symbolUniverseService = _serviceProvider.GetRequiredService<IPolygonSymbolUniverseService>();

        // Act
        var symbols = await symbolUniverseService.GetSymbolListAsync();

        // Assert
        symbols.Should().NotBeEmpty();
        symbols.Should().Contain(s => s.Market == "stocks");
        symbols.Should().Contain(s => s.Active);
        symbols.Should().AllSatisfy(s => s.Ticker.Should().NotBeNullOrEmpty());

        _output.WriteLine($"Fetched {symbols.Count()} symbols from Polygon API");
    }

    [Fact]
    [Trait("Category", "Integration")]
    public async Task DailyUniverseRefreshService_ShouldGenerateCandidates_UsingPolygonData()
    {
        // Skip if no API key provided
        var apiKey = Environment.GetEnvironmentVariable("POLY_API_KEY");
        if (string.IsNullOrEmpty(apiKey))
        {
            _output.WriteLine("Skipping integration test - POLY_API_KEY not provided");
            return;
        }

        // Arrange
        var universeRefreshService = _serviceProvider.GetRequiredService<IDailyUniverseRefreshService>();

        // Act
        var candidates = await universeRefreshService.RefreshUniverseAsync();

        // Assert
        candidates.Should().NotBeNull();
        candidates.Candidates.Should().NotBeEmpty();
        candidates.EvaluatedCount.Should().BeGreaterThan(0);
        candidates.CandidateCount.Should().BeGreaterThan(0);
        candidates.CandidateCount.Should().BeLessOrEqualTo(200); // Max candidates limit

        // Verify candidate quality
        candidates.Candidates.Should().AllSatisfy(c =>
        {
            c.Symbol.Should().NotBeNullOrEmpty();
            c.Price.Should().BeGreaterOrEqualTo(10.0m); // Min price filter
            c.AverageVolume.Should().BeGreaterOrEqualTo(1_000_000); // Min volume filter
            c.VolatilityPercent.Should().BeGreaterOrEqualTo(2.0m); // Min volatility filter
            c.RankingScore.Should().BeGreaterThan(0);
        });

        _output.WriteLine($"Generated {candidates.CandidateCount} candidates from {candidates.EvaluatedCount} symbols");
        _output.WriteLine($"Generation time: {candidates.Metrics.GenerationTime.TotalSeconds:F1}s");
    }

    [Fact]
    [Trait("Category", "Integration")]
    public async Task DynamicUniverseProvider_ShouldUsePolygonUniverse_WhenEnabled()
    {
        // Skip if no API key provided
        var apiKey = Environment.GetEnvironmentVariable("POLY_API_KEY");
        if (string.IsNullOrEmpty(apiKey))
        {
            _output.WriteLine("Skipping integration test - POLY_API_KEY not provided");
            return;
        }

        // Arrange
        var universeProvider = _serviceProvider.GetRequiredService<IDynamicUniverseProvider>();

        // Act
        var universe = await universeProvider.BuildUniverseAsync();

        // Assert
        universe.Should().NotBeEmpty();
        universe.Should().HaveCountLessOrEqualTo(200); // Max symbols limit

        _output.WriteLine($"Built universe with {universe.Count()} symbols using Polygon integration");
    }

    [Fact]
    [Trait("Category", "Integration")]
    public async Task MarketScheduleCoordinatorService_ShouldProvideValidStatus()
    {
        // Arrange
        var scheduleCoordinator = _serviceProvider.GetRequiredService<IMarketScheduleCoordinatorService>();

        // Act
        var status = await scheduleCoordinator.GetStatusAsync();

        // Assert
        status.Should().NotBeNull();
        status.CurrentTimeEt.Should().BeCloseTo(DateTime.Now, TimeSpan.FromMinutes(5)); // Rough timezone check
        status.NextEventTime.Should().BeAfter(status.CurrentTimeEt);
        status.UniverseRefreshTime.Should().NotBe(default);
        status.SubscriptionSetupTime.Should().NotBe(default);
        status.TradingStartTime.Should().NotBe(default);
        status.TradingEndTime.Should().NotBe(default);

        _output.WriteLine($"Market schedule status: IsOpen={status.IsMarketOpen}, NextEvent={status.NextEventTime}");
    }

    [Fact]
    [Trait("Category", "Integration")]
    public async Task CompleteWorkflow_ShouldExecuteSuccessfully()
    {
        // Skip if no API key provided
        var apiKey = Environment.GetEnvironmentVariable("POLY_API_KEY");
        if (string.IsNullOrEmpty(apiKey))
        {
            _output.WriteLine("Skipping integration test - POLY_API_KEY not provided");
            return;
        }

        // Arrange
        var symbolUniverseService = _serviceProvider.GetRequiredService<IPolygonSymbolUniverseService>();
        var universeRefreshService = _serviceProvider.GetRequiredService<IDailyUniverseRefreshService>();
        var subscriptionManager = _serviceProvider.GetRequiredService<IWebSocketSymbolSubscriptionManager>();
        var scheduleCoordinator = _serviceProvider.GetRequiredService<IMarketScheduleCoordinatorService>();

        // Act & Assert - Step 1: Fetch symbol universe
        _output.WriteLine("Step 1: Fetching symbol universe...");
        var symbols = await symbolUniverseService.GetSymbolListAsync();
        symbols.Should().NotBeEmpty();
        _output.WriteLine($"✓ Fetched {symbols.Count()} symbols");

        // Act & Assert - Step 2: Generate daily candidates
        _output.WriteLine("Step 2: Generating daily candidates...");
        var candidates = await universeRefreshService.RefreshUniverseAsync();
        candidates.Should().NotBeNull();
        candidates.Candidates.Should().NotBeEmpty();
        _output.WriteLine($"✓ Generated {candidates.CandidateCount} candidates");

        // Act & Assert - Step 3: Setup subscriptions
        _output.WriteLine("Step 3: Setting up WebSocket subscriptions...");
        await subscriptionManager.RefreshSubscriptionsAsync();
        var subscriptionStatus = await subscriptionManager.GetStatusAsync();
        subscriptionStatus.Should().NotBeNull();
        _output.WriteLine($"✓ Subscribed to {subscriptionStatus.SubscribedSymbolCount} symbols");

        // Act & Assert - Step 4: Check schedule coordination
        _output.WriteLine("Step 4: Checking schedule coordination...");
        var scheduleStatus = await scheduleCoordinator.GetStatusAsync();
        scheduleStatus.Should().NotBeNull();
        _output.WriteLine($"✓ Schedule status: {scheduleStatus.IsMarketOpen}");

        _output.WriteLine("Complete workflow executed successfully!");
    }

    [Fact]
    [Trait("Category", "Integration")]
    public async Task CacheIntegration_ShouldWorkCorrectly()
    {
        // Skip if no API key provided
        var apiKey = Environment.GetEnvironmentVariable("POLY_API_KEY");
        if (string.IsNullOrEmpty(apiKey))
        {
            _output.WriteLine("Skipping integration test - POLY_API_KEY not provided");
            return;
        }

        // Arrange
        var universeRefreshService = _serviceProvider.GetRequiredService<IDailyUniverseRefreshService>();

        // Act - First call should fetch fresh data
        _output.WriteLine("First call - fetching fresh data...");
        var firstResult = await universeRefreshService.RefreshUniverseAsync();
        firstResult.Should().NotBeNull();

        // Act - Second call should use cached data if available
        _output.WriteLine("Second call - checking cache...");
        var cachedResult = await universeRefreshService.GetCurrentCandidatesAsync();

        // Assert
        if (cachedResult != null)
        {
            cachedResult.CandidateCount.Should().Be(firstResult.CandidateCount);
            _output.WriteLine($"✓ Cache working - {cachedResult.CandidateCount} candidates cached");
        }
        else
        {
            _output.WriteLine("Cache not available (Redis not configured)");
        }
    }

    public void Dispose()
    {
        _host?.Dispose();
    }
}

/// <summary>
/// Mock WebSocket client for testing
/// </summary>
public class MockPolygonWebSocketClient : IPolygonWebSocketClient
{
    public event EventHandler<PolygonConnectionStatusEventArgs>? ConnectionStatusChanged;
    public event EventHandler<PolygonIndexUpdateEventArgs>? IndexUpdated;
    public event EventHandler<PolygonTradeUpdateEventArgs>? TradeUpdated;
    public event EventHandler<PolygonQuoteUpdateEventArgs>? QuoteUpdated;
    public event EventHandler<PolygonAggregateUpdateEventArgs>? AggregateUpdated;
    public event EventHandler<PolygonErrorEventArgs>? ErrorOccurred;

    public PolygonConnectionStatus ConnectionStatus => PolygonConnectionStatus.Authenticated;

    public Task ConnectAsync(CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task DisconnectAsync(CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task SubscribeToIndexUpdatesAsync(IEnumerable<string> indexSymbols, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task SubscribeToTradeUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task SubscribeToQuoteUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task SubscribeToAggregateUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task SubscribeToEquityUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task UnsubscribeFromIndexUpdatesAsync(IEnumerable<string> indexSymbols, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task UnsubscribeFromEquityUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task UnsubscribeAllAsync(CancellationToken cancellationToken = default) => Task.CompletedTask;

    public void Dispose() { }
}

using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using SmaTrendFollower.Tests.TestHelpers;
using StackExchange.Redis;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

[TestTimeout(TestTimeouts.Unit)]
[Trait("Category", TestCategories.Unit)]
public class VIXResolverServiceTests : IDisposable
{
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<IPolygonClientFactory> _mockPolygonFactory;
    private readonly Mock<IAlpacaClientFactory> _mockAlpacaFactory;
    private readonly Mock<IOptimizedRedisConnectionService> _mockRedisService;
    private readonly Mock<IVixFallbackService> _mockVixFallbackService;
    private readonly Mock<ISyntheticVixService> _mockSyntheticVixService;
    private readonly Mock<IDatabase> _mockRedis;
    private readonly Mock<ILogger<VIXResolverService>> _mockLogger;
    private readonly IConfiguration _configuration;
    private readonly VIXResolverService _service;

    public VIXResolverServiceTests()
    {
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockPolygonFactory = new Mock<IPolygonClientFactory>();
        _mockAlpacaFactory = new Mock<IAlpacaClientFactory>();
        _mockRedisService = new Mock<IOptimizedRedisConnectionService>();
        _mockVixFallbackService = new Mock<IVixFallbackService>();
        _mockSyntheticVixService = new Mock<ISyntheticVixService>();
        _mockRedis = new Mock<IDatabase>();
        _mockLogger = new Mock<ILogger<VIXResolverService>>();

        _configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["VixResolver:FreshnessThreshold"] = "00:15:00", // 15 minutes
                ["VixResolver:CacheStaleThreshold"] = "01:00:00", // 1 hour
                ["VixResolver:MaxRetryAttempts"] = "3",
                ["VixResolver:RetryDelay"] = "00:00:05", // 5 seconds
                ["VixResolver:EnableSyntheticCalculation"] = "true",
                ["VixResolver:EnableWebScraping"] = "true",
                ["VixResolver:AllowStaleCache"] = "true",
                ["VixResolver:HaltTradingOnFailure"] = "true"
            })
            .Build();

        _mockRedisService.Setup(x => x.GetDatabaseAsync()).ReturnsAsync(_mockRedis.Object);

        _service = new VIXResolverService(
            _mockMarketDataService.Object,
            _mockPolygonFactory.Object,
            _mockAlpacaFactory.Object,
            _mockRedisService.Object,
            _mockVixFallbackService.Object,
            _mockSyntheticVixService.Object,
            _configuration,
            _mockLogger.Object);
    }

    [Fact]
    public async Task GetCurrentVixAsync_WithFreshData_ShouldReturnVixValue()
    {
        // Arrange
        var expectedVix = 18.5m;
        var vixBars = CreateMockVixBars(expectedVix);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync("VIX", It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(vixBars);

        // Act
        var result = await _service.GetCurrentVixAsync();

        // Assert
        result.Should().NotBeNull();
        result.VixValue.Should().Be(expectedVix);
        result.IsFresh.Should().BeTrue();
        result.FallbackLevel.Should().Be(VixFallbackLevel.PolygonWebSocket);
        result.Quality.Should().Be(VixDataQuality.NearRealTime);
    }

    [Fact]
    public async Task GetCurrentVixAsync_WithStaleData_ShouldTryFallbackLevels()
    {
        // Arrange
        var staleVix = 20.0m;
        var freshVix = 19.5m;
        
        // First level returns stale data
        var staleBars = CreateMockVixBars(staleVix, DateTime.UtcNow.AddMinutes(-30));
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync("VIX", It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(staleBars);

        // Fallback service returns fresh data
        _mockVixFallbackService.Setup(x => x.CalculateSyntheticVixAsync())
            .ReturnsAsync(freshVix);

        // Act
        var result = await _service.GetCurrentVixAsync();

        // Assert
        result.Should().NotBeNull();
        result.VixValue.Should().Be(freshVix);
        result.FallbackLevel.Should().Be(VixFallbackLevel.SyntheticAlpaca);
        result.Quality.Should().Be(VixDataQuality.Synthetic);
    }

    [Fact]
    public async Task GetCurrentVixAsync_AllLevelsFail_ShouldRequestTradingHalt()
    {
        // Arrange
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync("VIX", It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<IIndexBar>());
        
        _mockVixFallbackService.Setup(x => x.CalculateSyntheticVixAsync())
            .ReturnsAsync((decimal?)null);
        _mockVixFallbackService.Setup(x => x.CalculateSyntheticVixFromPolygonAsync())
            .ReturnsAsync((decimal?)null);
        _mockVixFallbackService.Setup(x => x.GetVixFromWebAsync())
            .ReturnsAsync((decimal?)null);
        _mockVixFallbackService.Setup(x => x.GetVixFromBraveSearchAsync())
            .ReturnsAsync((decimal?)null);
        _mockVixFallbackService.Setup(x => x.GetVixWithCachingAsync())
            .ReturnsAsync((decimal?)null);

        var tradingHaltRequested = false;
        _service.TradingHaltRequested += (_, _) => tradingHaltRequested = true;

        // Act
        var result = await _service.GetCurrentVixAsync();

        // Assert
        result.Should().NotBeNull();
        result.VixValue.Should().BeNull();
        result.ShouldHaltTrading.Should().BeTrue();
        result.FallbackLevel.Should().Be(VixFallbackLevel.TradingHalt);
        tradingHaltRequested.Should().BeTrue();
    }

    [Fact]
    public async Task GetVixWithFreshnessAsync_WithCustomThreshold_ShouldRespectThreshold()
    {
        // Arrange
        var customThreshold = TimeSpan.FromMinutes(5);
        var vixValue = 22.0m;
        var dataAge = TimeSpan.FromMinutes(3); // Within custom threshold
        
        var vixBars = CreateMockVixBars(vixValue, DateTime.UtcNow.Subtract(dataAge));
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync("VIX", It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(vixBars);

        // Act
        var result = await _service.GetVixWithFreshnessAsync(customThreshold);

        // Assert
        result.Should().NotBeNull();
        result.VixValue.Should().Be(vixValue);
        result.IsFresh.Should().BeTrue();
        result.DataAge.Should().BeCloseTo(dataAge, TimeSpan.FromSeconds(5));
    }

    [Fact]
    public async Task IsVixDataFreshAsync_WithFreshData_ShouldReturnTrue()
    {
        // Arrange
        var vixBars = CreateMockVixBars(18.0m);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync("VIX", It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(vixBars);

        // Get fresh data first
        await _service.GetCurrentVixAsync();

        // Act
        var result = await _service.IsVixDataFreshAsync();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task GetVixDataAgeMinutesAsync_WithData_ShouldReturnAge()
    {
        // Arrange
        var dataAge = TimeSpan.FromMinutes(8);
        var vixBars = CreateMockVixBars(17.5m, DateTime.UtcNow.Subtract(dataAge));
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync("VIX", It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(vixBars);

        // Get data first
        await _service.GetCurrentVixAsync();

        // Act
        var result = await _service.GetVixDataAgeMinutesAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeCloseTo(dataAge.TotalMinutes, 1.0); // Within 1 minute tolerance
    }

    [Fact]
    public async Task RefreshVixDataAsync_ShouldForceRefresh()
    {
        // Arrange
        var oldVix = 20.0m;
        var newVix = 21.0m;
        
        var oldBars = CreateMockVixBars(oldVix);
        var newBars = CreateMockVixBars(newVix);
        
        _mockMarketDataService.SetupSequence(x => x.GetIndexBarsAsync("VIX", It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(oldBars)
            .ReturnsAsync(newBars);

        // Get initial data
        var initialResult = await _service.GetCurrentVixAsync();

        // Act
        var refreshedResult = await _service.RefreshVixDataAsync();

        // Assert
        initialResult.VixValue.Should().Be(oldVix);
        refreshedResult.VixValue.Should().Be(newVix);
    }

    [Theory]
    [InlineData(VixFallbackLevel.PolygonLastTrade)]
    [InlineData(VixFallbackLevel.PolygonWebSocket)]
    [InlineData(VixFallbackLevel.SyntheticAlpaca)]
    [InlineData(VixFallbackLevel.SyntheticPolygon)]
    [InlineData(VixFallbackLevel.GoogleSearch)]
    [InlineData(VixFallbackLevel.BraveSearch)]
    [InlineData(VixFallbackLevel.RedisCache)]
    public async Task GetVixFromSpecificLevel_ShouldReturnDataFromCorrectLevel(VixFallbackLevel level)
    {
        // Arrange
        var expectedVix = 19.0m;
        SetupMockForFallbackLevel(level, expectedVix);

        // Act
        VixDataPoint? result = level switch
        {
            VixFallbackLevel.PolygonLastTrade => await _service.GetVixFromPolygonLastTradeAsync(),
            VixFallbackLevel.PolygonWebSocket => await _service.GetVixFromPolygonWebSocketAsync(),
            VixFallbackLevel.SyntheticAlpaca => await _service.GetSyntheticVixFromAlpacaAsync(),
            VixFallbackLevel.SyntheticPolygon => await _service.GetSyntheticVixFromPolygonAsync(),
            VixFallbackLevel.GoogleSearch => await _service.GetVixFromGoogleSearchAsync(),
            VixFallbackLevel.BraveSearch => await _service.GetVixFromBraveSearchAsync(),
            VixFallbackLevel.RedisCache => await _service.GetVixFromCacheAsync(),
            _ => null
        };

        // Assert
        result.Should().NotBeNull();
        result!.Value.Should().Be(expectedVix);
        result.Source.Should().NotBeEmpty();
    }

    [Fact]
    public async Task GetFallbackStatisticsAsync_ShouldReturnStatistics()
    {
        // Act
        var result = await _service.GetFallbackStatisticsAsync();

        // Assert
        result.Should().NotBeNull();
        result.LastUpdated.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
        result.CurrentPreferredLevel.Should().Be(VixFallbackLevel.PolygonLastTrade);
    }

    [Fact]
    public async Task GetRetrievalHistoryAsync_ShouldReturnHistory()
    {
        // Arrange
        var vixBars = CreateMockVixBars(18.5m);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync("VIX", It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(vixBars);

        // Generate some history
        await _service.GetCurrentVixAsync();

        // Act
        var result = await _service.GetRetrievalHistoryAsync(1);

        // Assert
        result.Should().NotBeNull();
        // Note: In a real implementation, this would return actual historical data
    }

    [Fact]
    public void GetCurrentFallbackLevel_ShouldReturnCurrentLevel()
    {
        // Act
        var result = _service.GetCurrentFallbackLevel();

        // Assert
        result.Should().Be(VixFallbackLevel.PolygonLastTrade); // Default initial level
    }

    private List<IIndexBar> CreateMockVixBars(decimal vixValue, DateTime? timestamp = null)
    {
        var bars = new List<IIndexBar>();
        var mockBar = new Mock<IIndexBar>();
        
        mockBar.Setup(x => x.Close).Returns(vixValue);
        mockBar.Setup(x => x.Open).Returns(vixValue - 0.5m);
        mockBar.Setup(x => x.High).Returns(vixValue + 1.0m);
        mockBar.Setup(x => x.Low).Returns(vixValue - 1.0m);
        mockBar.Setup(x => x.Timestamp).Returns(timestamp ?? DateTime.UtcNow);
        
        bars.Add(mockBar.Object);
        return bars;
    }

    private void SetupMockForFallbackLevel(VixFallbackLevel level, decimal vixValue)
    {
        switch (level)
        {
            case VixFallbackLevel.PolygonWebSocket:
                var bars = CreateMockVixBars(vixValue);
                _mockMarketDataService.Setup(x => x.GetIndexBarsAsync("VIX", It.IsAny<DateTime>(), It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
                    .ReturnsAsync(bars);
                break;
            case VixFallbackLevel.SyntheticAlpaca:
                _mockVixFallbackService.Setup(x => x.CalculateSyntheticVixAsync())
                    .ReturnsAsync(vixValue);
                break;
            case VixFallbackLevel.SyntheticPolygon:
                _mockVixFallbackService.Setup(x => x.CalculateSyntheticVixFromPolygonAsync())
                    .ReturnsAsync(vixValue);
                break;
            case VixFallbackLevel.GoogleSearch:
                _mockVixFallbackService.Setup(x => x.GetVixFromWebAsync())
                    .ReturnsAsync(vixValue);
                break;
            case VixFallbackLevel.BraveSearch:
                _mockVixFallbackService.Setup(x => x.GetVixFromBraveSearchAsync())
                    .ReturnsAsync(vixValue);
                break;
            case VixFallbackLevel.RedisCache:
                _mockVixFallbackService.Setup(x => x.GetVixWithCachingAsync())
                    .ReturnsAsync(vixValue);
                _mockRedis.Setup(x => x.StringGetAsync("vix:timestamp", It.IsAny<CommandFlags>()))
                    .ReturnsAsync(DateTime.UtcNow.AddMinutes(-30).ToString()); // 30 minutes old
                break;
        }
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}
